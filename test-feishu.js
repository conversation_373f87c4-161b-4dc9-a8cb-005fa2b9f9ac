// 飞书API测试脚本
const { Client } = require('@larksuiteoapi/node-sdk');

// 测试配置（使用示例数据）
const testConfig = {
  appId: 'cli_12131231233',
  appSecret: 'aaaaaaaaaaxxxxxxxx',
  appToken: 'EIk0bJYLca0Tfcs9BixcfadQnZb',
  tableId: 'tbloBDbEseJUXE3Y',
};

const client = new Client({
  appId: testConfig.appId,
  appSecret: testConfig.appSecret,
  disableTokenCache: false,
});

async function testFeishuAPI() {
  console.log('🚀 开始测试飞书API...\n');

  try {
    // 1. 测试获取表格列表
    console.log('1. 测试获取表格列表...');
    const tablesResponse = await client.bitable.v1.appTable.list({
      path: {
        app_token: testConfig.appToken,
      },
      params: {
        page_size: 10,
      },
    });
    
    if (tablesResponse.code === 0) {
      console.log('✅ 表格列表获取成功');
      console.log('   表格数量:', tablesResponse.data?.items?.length || 0);
      tablesResponse.data?.items?.forEach(table => {
        console.log(`   - ${table.name} (${table.table_id})`);
      });
    } else {
      console.log('❌ 表格列表获取失败:', tablesResponse.msg);
    }

    console.log('');

    // 2. 测试获取字段列表
    console.log('2. 测试获取字段列表...');
    const fieldsResponse = await client.bitable.v1.appTableField.list({
      path: {
        app_token: testConfig.appToken,
        table_id: testConfig.tableId,
      },
      params: {
        page_size: 50,
      },
    });

    if (fieldsResponse.code === 0) {
      console.log('✅ 字段列表获取成功');
      console.log('   字段数量:', fieldsResponse.data?.items?.length || 0);
      fieldsResponse.data?.items?.forEach(field => {
        console.log(`   - ${field.field_name} (${field.field_id}) - ${field.ui_type}`);
      });
    } else {
      console.log('❌ 字段列表获取失败:', fieldsResponse.msg);
    }

    console.log('');

    // 3. 测试获取记录数据
    console.log('3. 测试获取记录数据...');
    const recordsResponse = await client.bitable.v1.appTableRecord.search({
      path: {
        app_token: testConfig.appToken,
        table_id: testConfig.tableId,
      },
      params: {
        page_size: 5,
      },
      data: {
        automatic_fields: false,
      },
    });

    if (recordsResponse.code === 0) {
      console.log('✅ 记录数据获取成功');
      console.log('   记录数量:', recordsResponse.data?.items?.length || 0);
      console.log('   总记录数:', recordsResponse.data?.total || 0);
      
      // 统计数据（按新逻辑）
      let totalCount = 0;      // 注册=true AND 促销=true
      let soldCount = 0;       // 已售=true
      let availableCount = 0;  // 注册=true AND 促销=true AND 已售=false

      recordsResponse.data?.items?.forEach((record, index) => {
        const registered = record.fields?.注册 === true;
        const promotion = record.fields?.促销 === true;
        const sold = record.fields?.已售 === true;
        const account = record.fields?.账号?.[0]?.text || '未知账号';

        // 总账号数逻辑
        if (registered && promotion) {
          totalCount++;
        }

        // 已售数量
        if (sold) {
          soldCount++;
        }

        // 库存数量
        if (registered && promotion && !sold) {
          availableCount++;
        }

        const status = [];
        if (registered) status.push('已注册');
        if (promotion) status.push('促销');
        if (sold) status.push('已售');
        if (registered && promotion && !sold) status.push('库存');

        console.log(`   ${index + 1}. ${account} - [${status.join(', ')}]`);
      });

      console.log(`   统计: 总账号 ${totalCount} 个, 已售 ${soldCount} 个, 库存 ${availableCount} 个`);
    } else {
      console.log('❌ 记录数据获取失败:', recordsResponse.msg);
    }

    console.log('');

    // 4. 测试查询库存账号（注册=true AND 促销=true AND 已售=false）
    console.log('4. 测试查询库存账号...');
    const availableResponse = await client.bitable.v1.appTableRecord.search({
      path: {
        app_token: testConfig.appToken,
        table_id: testConfig.tableId,
      },
      params: {
        page_size: 3,
      },
      data: {
        filter: {
          conjunction: 'and',
          conditions: [
            {
              field_name: '注册',
              operator: 'is',
              value: ['true'],
            },
            {
              field_name: '促销',
              operator: 'is',
              value: ['true'],
            },
            {
              field_name: '已售',
              operator: 'is',
              value: ['false'],
            },
          ],
        },
        automatic_fields: false,
      },
    });

    if (availableResponse.code === 0) {
      console.log('✅ 库存账号查询成功');
      console.log('   库存账号数量:', availableResponse.data?.items?.length || 0);
      availableResponse.data?.items?.forEach((record, index) => {
        const account = record.fields?.账号?.[0]?.text || '未知账号';
        const registered = record.fields?.注册 === true ? '已注册' : '未注册';
        const promotion = record.fields?.促销 === true ? '促销' : '非促销';
        console.log(`   ${index + 1}. ${account} - ${registered}, ${promotion}, 库存`);
      });
    } else {
      console.log('❌ 库存账号查询失败:', availableResponse.msg);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response?.data) {
      console.error('   错误详情:', JSON.stringify(error.response.data, null, 2));
    }
  }

  console.log('\n🏁 测试完成');
}

// 运行测试
testFeishuAPI();
