// 订单状态枚举
export enum OrderStatus {
  PENDING = 'pending',
  PAID = 'paid',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// 支付状态枚举
export enum PaymentStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

// 订单接口
export interface Order {
  id: string;
  userEmail: string;
  amount: number;
  status: OrderStatus;
  paymentId?: string;
  augmentEmail?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 支付接口
export interface Payment {
  id: string;
  orderId: string;
  amount: number;
  status: PaymentStatus;
  wechatOrderId?: string;
  qrCodeUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 微信支付请求接口
export interface WechatPayRequest {
  appid: string;
  mchid: string;
  description: string;
  out_trade_no: string;
  amount: {
    total: number;
    currency: string;
  };
  notify_url: string;
}

// 微信支付响应接口
export interface WechatPayResponse {
  code_url: string;
}

// API 响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 创建订单请求
export interface CreateOrderRequest {
  userEmail: string;
}

// 创建订单响应
export interface CreateOrderResponse {
  orderId: string;
  qrCodeUrl: string;
  amount: number;
}

// 查询订单状态响应
export interface OrderStatusResponse {
  orderId: string;
  status: OrderStatus;
  augmentEmail?: string;
}
