import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// 客户端 Supabase 实例（用于前端）
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 服务端 Supabase 实例（用于后端 API，具有更高权限）
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// 数据库表名常量
export const TABLES = {
  ORDERS: 'orders',
  PAYMENTS: 'payments',
  AUGMENT_ACCOUNTS: 'augment_accounts'
} as const;
