import { PrismaClient } from '@prisma/client';
import { Order, Payment, OrderStatus, PaymentStatus } from '@/types';

// 全局 Prisma 客户端实例
declare global {
  var prisma: PrismaClient | undefined;
}

export const prisma = globalThis.prisma || new PrismaClient();

if (process.env.NODE_ENV !== 'production') {
  globalThis.prisma = prisma;
}

// 订单相关操作
export class OrderService {
  // 创建订单
  static async createOrder(userEmail: string, amount: number): Promise<Order> {
    const order = await prisma.order.create({
      data: {
        userEmail,
        amount,
        status: 'PENDING'
      }
    });
    
    return {
      id: order.id,
      userEmail: order.userEmail,
      amount: order.amount,
      status: order.status as OrderStatus,
      augmentEmail: order.augmentEmail || undefined,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt
    };
  }

  // 根据ID获取订单
  static async getOrderById(orderId: string): Promise<Order | null> {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: { payments: true }
    });

    if (!order) return null;

    return {
      id: order.id,
      userEmail: order.userEmail,
      amount: order.amount,
      status: order.status as OrderStatus,
      augmentEmail: order.augmentEmail || undefined,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt
    };
  }

  // 更新订单状态
  static async updateOrderStatus(orderId: string, status: OrderStatus): Promise<Order | null> {
    const order = await prisma.order.update({
      where: { id: orderId },
      data: { 
        status,
        updatedAt: new Date()
      }
    });

    return {
      id: order.id,
      userEmail: order.userEmail,
      amount: order.amount,
      status: order.status as OrderStatus,
      augmentEmail: order.augmentEmail || undefined,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt
    };
  }

  // 分配 Augment 账号给订单
  static async assignAugmentAccount(orderId: string, augmentEmail: string): Promise<Order | null> {
    const order = await prisma.order.update({
      where: { id: orderId },
      data: { 
        augmentEmail,
        status: 'COMPLETED',
        updatedAt: new Date()
      }
    });

    return {
      id: order.id,
      userEmail: order.userEmail,
      amount: order.amount,
      status: order.status as OrderStatus,
      augmentEmail: order.augmentEmail || undefined,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt
    };
  }
}

// 支付相关操作
export class PaymentService {
  // 创建支付记录
  static async createPayment(
    orderId: string, 
    amount: number, 
    qrCodeUrl?: string
  ): Promise<Payment> {
    const payment = await prisma.payment.create({
      data: {
        orderId,
        amount,
        status: 'PENDING',
        qrCodeUrl
      }
    });

    return {
      id: payment.id,
      orderId: payment.orderId,
      amount: payment.amount,
      status: payment.status as PaymentStatus,
      wechatOrderId: payment.wechatOrderId || undefined,
      qrCodeUrl: payment.qrCodeUrl || undefined,
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt
    };
  }

  // 更新支付状态
  static async updatePaymentStatus(
    paymentId: string, 
    status: PaymentStatus,
    wechatOrderId?: string
  ): Promise<Payment | null> {
    const payment = await prisma.payment.update({
      where: { id: paymentId },
      data: { 
        status,
        wechatOrderId,
        updatedAt: new Date()
      }
    });

    return {
      id: payment.id,
      orderId: payment.orderId,
      amount: payment.amount,
      status: payment.status as PaymentStatus,
      wechatOrderId: payment.wechatOrderId || undefined,
      qrCodeUrl: payment.qrCodeUrl || undefined,
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt
    };
  }

  // 根据订单ID获取支付记录
  static async getPaymentByOrderId(orderId: string): Promise<Payment | null> {
    const payment = await prisma.payment.findFirst({
      where: { orderId },
      orderBy: { createdAt: 'desc' }
    });

    if (!payment) return null;

    return {
      id: payment.id,
      orderId: payment.orderId,
      amount: payment.amount,
      status: payment.status as PaymentStatus,
      wechatOrderId: payment.wechatOrderId || undefined,
      qrCodeUrl: payment.qrCodeUrl || undefined,
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt
    };
  }
}
