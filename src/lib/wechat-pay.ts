import crypto from 'crypto';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { generateNonceStr } from './utils';

// 微信支付 API v3 配置
const WECHAT_CONFIG = {
  appId: process.env.WECHAT_APP_ID!,
  mchId: process.env.WECHAT_MCH_ID!,
  apiV3Key: process.env.WECHAT_API_V3_KEY!,
  certPath: process.env.WECHAT_CERT_PATH!,
  keyPath: process.env.WECHAT_KEY_PATH!,
  certSerialNumber: process.env.WECHAT_CERT_SERIAL_NUMBER!,
  notifyUrl: `${process.env.NEXT_PUBLIC_APP_URL}/api/payment/notify`,
  baseUrl: 'https://api.mch.weixin.qq.com'
};

// 微信支付 API v3 Native 下单请求接口
interface WechatPayV3NativeRequest {
  appid: string;
  mchid: string;
  description: string;
  out_trade_no: string;
  notify_url: string;
  amount: {
    total: number;
    currency: string;
  };
  scene_info?: {
    payer_client_ip: string;
  };
}

// 微信支付 API v3 Native 下单响应接口
interface WechatPayV3NativeResponse {
  code_url: string;
}

// 微信支付 API v3 查询订单响应接口
interface WechatPayV3QueryResponse {
  appid: string;
  mchid: string;
  out_trade_no: string;
  transaction_id?: string;
  trade_type: string;
  trade_state: string;
  trade_state_desc: string;
  bank_type?: string;
  attach?: string;
  success_time?: string;
  payer: {
    openid: string;
  };
  amount: {
    total: number;
    payer_total?: number;
    currency: string;
    payer_currency?: string;
  };
}

// 微信支付 API v3 回调通知接口
interface WechatPayV3NotifyRequest {
  id: string;
  create_time: string;
  event_type: string;
  resource_type: string;
  resource: {
    original_type: string;
    algorithm: string;
    ciphertext: string;
    associated_data: string;
    nonce: string;
  };
  summary: string;
}

// 读取私钥文件
function getPrivateKey(): string {
  try {
    const keyPath = path.resolve(process.cwd(), WECHAT_CONFIG.keyPath);
    return fs.readFileSync(keyPath, 'utf8');
  } catch (error) {
    console.error('读取私钥文件失败:', error);
    throw new Error('私钥文件不存在或无法读取，请确保证书文件已正确配置');
  }
}

// 生成微信支付 API v3 签名
function generateV3Signature(method: string, url: string, timestamp: string, nonce: string, body: string): string {
  // 构造签名串
  const message = `${method}\n${url}\n${timestamp}\n${nonce}\n${body}\n`;

  try {
    const privateKey = getPrivateKey();

    // 使用 SHA256withRSA 签名
    const sign = crypto.createSign('RSA-SHA256');
    sign.update(message, 'utf8');
    const signature = sign.sign(privateKey, 'base64');

    console.log('=== 微信支付 API v3 签名调试 ===');
    console.log('1. 签名方法:', method);
    console.log('2. 请求URL:', url);
    console.log('3. 时间戳:', timestamp);
    console.log('4. 随机字符串:', nonce);
    console.log('5. 请求体:', body);
    console.log('6. 签名消息:', message);
    console.log('7. 生成签名:', signature);
    console.log('================================');

    return signature;
  } catch (error) {
    console.error('生成签名失败:', error);
    throw new Error('签名生成失败，请检查私钥文件');
  }
}

// 生成 Authorization 头
function generateAuthorizationHeader(method: string, url: string, body: string): string {
  const timestamp = Math.floor(Date.now() / 1000).toString();
  const nonce = generateNonceStr();
  const signature = generateV3Signature(method, url, timestamp, nonce, body);

  const serialNumber = WECHAT_CONFIG.certSerialNumber || 'CERT_SERIAL_NUMBER_PLACEHOLDER';

  return `WECHATPAY2-SHA256-RSA2048 mchid="${WECHAT_CONFIG.mchId}",nonce_str="${nonce}",signature="${signature}",timestamp="${timestamp}",serial_no="${serialNumber}"`;
}

// 验证微信支付 API v3 回调签名
export function verifyV3CallbackSignature(
  timestamp: string,
  nonce: string,
  body: string,
  signature: string,
  serialNumber: string
): boolean {
  try {
    // 构造验签名串
    const message = `${timestamp}\n${nonce}\n${body}\n`;

    // 这里应该使用微信支付平台证书来验证签名
    // 为了简化，我们先返回 true，实际生产环境需要实现完整的证书验证
    console.log('验证回调签名:', { timestamp, nonce, body: body.substring(0, 100), signature, serialNumber });

    return true; // 临时返回 true，实际需要用平台证书验证
  } catch (error) {
    console.error('验证回调签名失败:', error);
    return false;
  }
}

// 解密微信支付 API v3 回调数据
function decryptV3CallbackData(
  ciphertext: string,
  associatedData: string,
  nonce: string
): string {
  try {
    const key = WECHAT_CONFIG.apiV3Key;

    // 使用 AES-256-GCM 解密
    const decipher = crypto.createDecipherGCM('aes-256-gcm', Buffer.from(key, 'utf8'));
    decipher.setAAD(Buffer.from(associatedData, 'utf8'));

    const ciphertextBuffer = Buffer.from(ciphertext, 'base64');
    const tag = ciphertextBuffer.slice(-16);
    const encrypted = ciphertextBuffer.slice(0, -16);

    decipher.setAuthTag(tag);

    let decrypted = decipher.update(encrypted, undefined, 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    console.error('解密回调数据失败:', error);
    throw new Error('解密回调数据失败');
  }
}

// 创建微信支付 API v3 Native 订单
export async function createWechatPayOrder(
  orderId: string,
  amount: number, // 金额（分）
  description: string,
  clientIp: string = '127.0.0.1'
): Promise<{ success: boolean; qrCodeUrl?: string; error?: string }> {
  try {
    // 构造请求参数
    const requestData: WechatPayV3NativeRequest = {
      appid: WECHAT_CONFIG.appId,
      mchid: WECHAT_CONFIG.mchId,
      description: description,
      out_trade_no: orderId,
      notify_url: WECHAT_CONFIG.notifyUrl,
      amount: {
        total: amount,
        currency: 'CNY'
      },
      scene_info: {
        payer_client_ip: clientIp
      }
    };

    const requestBody = JSON.stringify(requestData);
    const url = '/v3/pay/transactions/native';
    const fullUrl = `${WECHAT_CONFIG.baseUrl}${url}`;

    // 生成 Authorization 头
    const authorization = generateAuthorizationHeader('POST', url, requestBody);

    console.log('=== 微信支付 API v3 请求调试 ===');
    console.log('1. 请求URL:', fullUrl);
    console.log('2. 请求体:', requestBody);
    console.log('3. Authorization:', authorization);
    console.log('================================');

    // 发送请求
    const response = await axios.post(fullUrl, requestBody, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': authorization,
        'User-Agent': 'Augment-Pay/1.0'
      },
      timeout: 10000,
    });

    console.log('微信支付 API v3 响应:', response.data);

    const responseData: WechatPayV3NativeResponse = response.data;

    return {
      success: true,
      qrCodeUrl: responseData.code_url
    };

  } catch (error) {
    console.error('创建微信支付订单失败:', error);

    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.message || error.message;
      console.error('API 错误详情:', error.response?.data);

      return {
        success: false,
        error: `微信支付API错误: ${errorMessage}`
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : '创建支付订单失败'
    };
  }
}

// 查询微信支付 API v3 订单状态
export async function queryWechatPayOrder(
  orderId: string
): Promise<{ success: boolean; isPaid?: boolean; error?: string }> {
  try {
    const url = `/v3/pay/transactions/out-trade-no/${orderId}?mchid=${WECHAT_CONFIG.mchId}`;
    const fullUrl = `${WECHAT_CONFIG.baseUrl}${url}`;

    // 生成 Authorization 头（GET 请求，body 为空字符串）
    const authorization = generateAuthorizationHeader('GET', url, '');

    const response = await axios.get(fullUrl, {
      headers: {
        'Accept': 'application/json',
        'Authorization': authorization,
        'User-Agent': 'Augment-Pay/1.0'
      },
      timeout: 10000,
    });

    const responseData: WechatPayV3QueryResponse = response.data;

    console.log('查询订单响应:', responseData);

    // 检查支付状态
    const isPaid = responseData.trade_state === 'SUCCESS';

    return {
      success: true,
      isPaid
    };

  } catch (error) {
    console.error('查询微信支付订单状态失败:', error);

    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.message || error.message;
      console.error('查询API错误详情:', error.response?.data);

      return {
        success: false,
        error: `查询订单失败: ${errorMessage}`
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : '查询支付状态失败'
    };
  }
}

// 处理微信支付 API v3 回调通知
export function parseV3WechatNotification(
  requestBody: string,
  signature: string,
  timestamp: string,
  nonce: string,
  serialNumber: string
): {
  success: boolean;
  data?: any;
  error?: string;
} {
  try {
    // 验证签名
    if (!verifyV3CallbackSignature(timestamp, nonce, requestBody, signature, serialNumber)) {
      return {
        success: false,
        error: '签名验证失败'
      };
    }

    const notifyData: WechatPayV3NotifyRequest = JSON.parse(requestBody);

    // 解密回调数据
    const decryptedData = decryptV3CallbackData(
      notifyData.resource.ciphertext,
      notifyData.resource.associated_data,
      notifyData.resource.nonce
    );

    const paymentData = JSON.parse(decryptedData);

    return {
      success: true,
      data: paymentData
    };

  } catch (error) {
    console.error('解析微信支付 API v3 回调失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '解析回调数据失败'
    };
  }
}

// 生成微信支付 API v3 回调响应
export function generateV3WechatNotificationResponse(success: boolean, message?: string): any {
  if (success) {
    return { code: 'SUCCESS', message: message || 'OK' };
  } else {
    return { code: 'FAIL', message: message || 'FAIL' };
  }
}
