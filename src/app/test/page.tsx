'use client';

import { useState } from 'react';

export default function TestPage() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testCreateOrder = async () => {
    try {
      addResult('开始测试创建订单...');
      
      const response = await fetch('/api/orders/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userEmail: '<EMAIL>'
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        addResult(`✅ 订单创建成功: ${data.data.orderId}`);
        addResult(`二维码URL: ${data.data.qrCodeUrl}`);
        return data.data.orderId;
      } else {
        addResult(`❌ 订单创建失败: ${data.error}`);
        return null;
      }
    } catch (error) {
      addResult(`❌ 订单创建异常: ${error}`);
      return null;
    }
  };

  const testOrderStatus = async (orderId: string) => {
    try {
      addResult(`开始查询订单状态: ${orderId}`);
      
      const response = await fetch(`/api/orders/status?orderId=${orderId}`);
      const data = await response.json();
      
      if (data.success) {
        addResult(`✅ 订单状态查询成功: ${data.data.status}`);
        if (data.data.augmentEmail) {
          addResult(`Augment账号: ${data.data.augmentEmail}`);
        }
      } else {
        addResult(`❌ 订单状态查询失败: ${data.error}`);
      }
    } catch (error) {
      addResult(`❌ 订单状态查询异常: ${error}`);
    }
  };

  const runFullTest = async () => {
    setIsLoading(true);
    setTestResults([]);
    
    try {
      // 测试创建订单
      const orderId = await testCreateOrder();
      
      if (orderId) {
        // 等待一秒后查询订单状态
        await new Promise(resolve => setTimeout(resolve, 1000));
        await testOrderStatus(orderId);
      }
      
      addResult('🎉 测试完成');
    } catch (error) {
      addResult(`❌ 测试异常: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            Augment 支付系统测试
          </h1>

          <div className="grid md:grid-cols-2 gap-8">
            {/* 测试控制面板 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">测试控制</h2>
              
              <div className="space-y-4">
                <button
                  onClick={runFullTest}
                  disabled={isLoading}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                >
                  {isLoading ? '测试中...' : '运行完整测试'}
                </button>

                <button
                  onClick={testCreateOrder}
                  disabled={isLoading}
                  className="w-full bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                >
                  测试创建订单
                </button>

                <button
                  onClick={clearResults}
                  className="w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                >
                  清空结果
                </button>
              </div>

              <div className="mt-8">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">环境检查</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Node.js 环境:</span>
                    <span className="text-green-600">✅ 正常</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Next.js 版本:</span>
                    <span className="text-green-600">✅ 最新</span>
                  </div>
                  <div className="flex justify-between">
                    <span>数据库连接:</span>
                    <span className="text-yellow-600">⚠️ 需配置</span>
                  </div>
                  <div className="flex justify-between">
                    <span>微信支付配置:</span>
                    <span className="text-yellow-600">⚠️ 需配置</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 测试结果 */}
            <div>
              <h2 className="text-xl font-semibold text-gray-800 mb-4">测试结果</h2>
              
              <div className="bg-gray-900 text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
                {testResults.length === 0 ? (
                  <div className="text-gray-500">等待测试结果...</div>
                ) : (
                  testResults.map((result, index) => (
                    <div key={index} className="mb-1">
                      {result}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* 配置说明 */}
          <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-yellow-800 mb-4">
              ⚠️ 配置说明
            </h3>
            <div className="text-yellow-700 space-y-2">
              <p>在运行测试之前，请确保已正确配置以下环境变量：</p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>DATABASE_URL - 数据库连接字符串</li>
                <li>WECHAT_APP_ID - 微信应用ID</li>
                <li>WECHAT_MCH_ID - 微信商户号</li>
                <li>WECHAT_API_KEY - 微信API密钥</li>
                <li>NEXT_PUBLIC_APP_URL - 应用URL</li>
                <li>AUGMENT_ACCOUNT_PRICE - 账号价格</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
