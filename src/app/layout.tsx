import type { Metadata } from "next";
import { Geist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: 'AugmentCode账号租赁 - 专业AI编程助手 | 600次/月独享账号',
  description: '专业的AugmentCode账号租赁平台，600次消息/月，独享账号，成品号即买即用，无需安装插件，适合开发者和企业使用。微信支付，即时交付。',
  keywords: 'AugmentCode, AI编程助手, 账号租赁, 代码助手, 开发工具, 人工智能, 编程辅助, 代码生成',
  authors: [{ name: 'AugmentCode租赁平台' }],
  robots: 'index, follow',
  openGraph: {
    title: 'AugmentCode账号租赁 - 专业AI编程助手',
    description: '600次消息/月，独享账号，成品号即买即用，无需安装插件',
    type: 'website',
    locale: 'zh_CN',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AugmentCode账号租赁 - 专业AI编程助手',
    description: '600次消息/月，独享账号，成品号即买即用',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": "AugmentCode账号租赁",
    "description": "专业的AugmentCode账号租赁平台，600次消息/月，独享账号，成品号即买即用，无需安装插件",
    "brand": {
      "@type": "Brand",
      "name": "AugmentCode"
    },
    "offers": {
      "@type": "Offer",
      "price": "99",
      "priceCurrency": "CNY",
      "availability": "https://schema.org/InStock",
      "validFrom": "2024-01-01",
      "priceValidUntil": "2024-12-31"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "150"
    }
  };

  return (
    <html lang="zh-CN">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
