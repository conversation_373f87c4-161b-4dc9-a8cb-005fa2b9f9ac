import { NextRequest, NextResponse } from 'next/server';
import { OrderService, PaymentService } from '@/lib/database';
import { queryWechatPayOrder } from '@/lib/wechat-pay';
import { allocateAccountToUser } from '@/lib/feishu';
import { ApiResponse, OrderStatusResponse } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('orderId');

    if (!orderId) {
      return NextResponse.json({
        success: false,
        error: '订单ID不能为空'
      } as ApiResponse, { status: 400 });
    }

    // 获取订单信息
    const order = await OrderService.getOrderById(orderId);

    if (!order) {
      return NextResponse.json({
        success: false,
        error: '订单不存在'
      } as ApiResponse, { status: 404 });
    }

    // 如果订单已完成，直接返回结果
    if (order.status === 'COMPLETED') {
      const response: OrderStatusResponse = {
        orderId: order.id,
        status: order.status,
        augmentEmail: order.augmentEmail
      };

      return NextResponse.json({
        success: true,
        data: response
      } as ApiResponse<OrderStatusResponse>);
    }

    // 如果订单状态为待支付或已支付，查询微信支付状态
    if (order.status === 'PENDING' || order.status === 'PAID') {
      const wechatPayResult = await queryWechatPayOrder(orderId);

      if (wechatPayResult.success && wechatPayResult.isPaid) {
        // 支付成功，更新订单状态
        await OrderService.updateOrderStatus(orderId, 'PAID');
        
        // 更新支付记录状态
        const payment = await PaymentService.getPaymentByOrderId(orderId);
        if (payment) {
          await PaymentService.updatePaymentStatus(payment.id, 'SUCCESS');
        }

        // 分配 Augment 账号
        const allocationResult = await allocateAccountToUser(order.userEmail);

        if (!allocationResult.success) {
          console.error('账号分配失败:', allocationResult.error);
          return NextResponse.json({
            success: false,
            error: '账号分配失败，请联系客服'
          } as ApiResponse<OrderStatusResponse>, { status: 500 });
        }

        const allocatedAccount = allocationResult.account!;
        await OrderService.assignAugmentAccount(orderId, allocatedAccount.account);

        const response: OrderStatusResponse = {
          orderId: order.id,
          status: 'COMPLETED',
          augmentEmail: allocatedAccount.account
        };

        return NextResponse.json({
          success: true,
          data: response
        } as ApiResponse<OrderStatusResponse>);
      }
    }

    // 返回当前订单状态
    const response: OrderStatusResponse = {
      orderId: order.id,
      status: order.status,
      augmentEmail: order.augmentEmail
    };

    return NextResponse.json({
      success: true,
      data: response
    } as ApiResponse<OrderStatusResponse>);

  } catch (error) {
    console.error('查询订单状态失败:', error);
    
    return NextResponse.json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse, { status: 500 });
  }
}
