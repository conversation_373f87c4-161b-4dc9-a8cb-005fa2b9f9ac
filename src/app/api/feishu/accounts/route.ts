import { NextResponse } from 'next/server';
import { getAvailableAccounts } from '@/lib/feishu';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    
    const accounts = await getAvailableAccounts(limit);
    
    return NextResponse.json({
      success: true,
      data: accounts,
    });
  } catch (error) {
    console.error('获取可用账号失败:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: '获取可用账号失败',
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}
