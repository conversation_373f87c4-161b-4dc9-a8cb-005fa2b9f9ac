import { NextResponse } from 'next/server';
import { getAccountStats } from '@/lib/feishu';

export async function GET() {
  try {
    const stats = await getAccountStats();
    
    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('获取飞书账号统计失败:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: '获取账号统计失败',
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}
