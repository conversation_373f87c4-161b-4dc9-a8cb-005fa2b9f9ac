import { NextResponse } from 'next/server';
import { testFeishuConnection } from '@/lib/feishu';

export async function GET() {
  try {
    const isConnected = await testFeishuConnection();
    
    if (isConnected) {
      return NextResponse.json({
        success: true,
        message: '飞书连接成功',
        data: {
          connected: true,
          timestamp: new Date().toISOString(),
        },
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: '飞书连接失败',
          data: {
            connected: false,
            timestamp: new Date().toISOString(),
          },
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('飞书连接测试失败:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: '飞书连接测试失败',
        message: error instanceof Error ? error.message : '未知错误',
        data: {
          connected: false,
          timestamp: new Date().toISOString(),
        },
      },
      { status: 500 }
    );
  }
}
