import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // 简单的健康检查
    return NextResponse.json(
      { 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        service: 'aug-pay'
      },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json(
      { 
        status: 'error', 
        timestamp: new Date().toISOString(),
        service: 'aug-pay'
      },
      { status: 500 }
    );
  }
}
