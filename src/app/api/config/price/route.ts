import { NextResponse } from 'next/server';
import { ApiResponse } from '@/types';

interface PriceConfigResponse {
  price: number;
  priceDisplay: string;
}

export async function GET() {
  try {
    // 获取价格配置
    const price = parseFloat(process.env.AUGMENT_ACCOUNT_PRICE || '99');
    
    const response: PriceConfigResponse = {
      price: price,
      priceDisplay: `¥${price}`
    };

    return NextResponse.json({
      success: true,
      data: response
    } as ApiResponse<PriceConfigResponse>);

  } catch (error) {
    console.error('获取价格配置失败:', error);
    
    return NextResponse.json({
      success: false,
      error: '服务器内部错误'
    } as ApiResponse, { status: 500 });
  }
}
