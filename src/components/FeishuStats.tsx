'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Database, CheckCircle, XCircle, TrendingUp } from 'lucide-react';

interface AccountStats {
  total: number;
  sold: number;
  available: number;
  soldRate: string;
}

interface AccountRecord {
  recordId: string;
  account: string;
  registered: boolean;
  promotion: boolean;
  sold: boolean;
  registrationDate?: number;
  email?: string;
}

export default function FeishuStats() {
  const [stats, setStats] = useState<AccountStats | null>(null);
  const [accounts, setAccounts] = useState<AccountRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<boolean | null>(null);

  // 获取统计数据
  const fetchStats = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/feishu/stats');
      const data = await response.json();
      
      if (data.success) {
        setStats(data.data);
      } else {
        setError(data.message || '获取统计数据失败');
      }
    } catch (err) {
      setError('网络请求失败');
      console.error('获取统计数据失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 获取可用账号
  const fetchAccounts = async () => {
    try {
      const response = await fetch('/api/feishu/accounts?limit=5');
      const data = await response.json();
      
      if (data.success) {
        setAccounts(data.data);
      }
    } catch (err) {
      console.error('获取可用账号失败:', err);
    }
  };

  // 测试连接
  const testConnection = async () => {
    try {
      const response = await fetch('/api/feishu/test');
      const data = await response.json();
      setConnectionStatus(data.success);
    } catch (err) {
      setConnectionStatus(false);
      console.error('连接测试失败:', err);
    }
  };

  // 刷新所有数据
  const refreshAll = async () => {
    await Promise.all([
      fetchStats(),
      fetchAccounts(),
      testConnection(),
    ]);
  };

  // 初始加载
  useEffect(() => {
    refreshAll();
  }, []);

  // 格式化日期
  const formatDate = (timestamp?: number) => {
    if (!timestamp) return '-';
    return new Date(timestamp).toLocaleDateString('zh-CN');
  };

  return (
    <div className="space-y-6">
      {/* 标题和刷新按钮 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">飞书账号管理</h2>
          <p className="text-gray-600">实时查看账号库存和销售统计</p>
        </div>
        <div className="flex items-center gap-2">
          {/* 连接状态 */}
          <Badge variant={connectionStatus === true ? 'default' : connectionStatus === false ? 'destructive' : 'secondary'}>
            {connectionStatus === true ? (
              <>
                <CheckCircle className="w-3 h-3 mr-1" />
                已连接
              </>
            ) : connectionStatus === false ? (
              <>
                <XCircle className="w-3 h-3 mr-1" />
                连接失败
              </>
            ) : (
              <>
                <Database className="w-3 h-3 mr-1" />
                检测中
              </>
            )}
          </Badge>
          
          <Button 
            onClick={refreshAll} 
            disabled={loading}
            size="sm"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新数据
          </Button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center text-red-600">
              <XCircle className="w-5 h-5 mr-2" />
              {error}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总账号数</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total || 0}</div>
            <p className="text-xs text-muted-foreground">已注册且促销的账号</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已售账号</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats?.sold || 0}</div>
            <p className="text-xs text-muted-foreground">已成功销售</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">库存账号</CardTitle>
            <XCircle className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats?.available || 0}</div>
            <p className="text-xs text-muted-foreground">可供销售的库存</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">销售率</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats?.soldRate || '0%'}</div>
            <p className="text-xs text-muted-foreground">已售出/总账号数</p>
          </CardContent>
        </Card>
      </div>

      {/* 库存账号列表 */}
      <Card>
        <CardHeader>
          <CardTitle>最新库存账号</CardTitle>
          <CardDescription>显示前5个可销售的库存账号</CardDescription>
        </CardHeader>
        <CardContent>
          {accounts.length > 0 ? (
            <div className="space-y-3">
              {accounts.map((account) => (
                <div key={account.recordId} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium">{account.account}</div>
                    <div className="text-sm text-gray-500">
                      注册日期: {formatDate(account.registrationDate)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {account.registered && (
                      <Badge variant="secondary" className="text-xs">已注册</Badge>
                    )}
                    {account.promotion && (
                      <Badge variant="outline" className="text-xs">促销</Badge>
                    )}
                    <Badge variant="default" className="text-xs">可用</Badge>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              {loading ? '加载中...' : '暂无库存账号'}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
