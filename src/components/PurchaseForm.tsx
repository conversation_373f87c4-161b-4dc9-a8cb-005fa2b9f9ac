'use client';

import { useState, useEffect } from 'react';
import { isValidEmail } from '@/lib/utils';
import PaymentModal from './PaymentModal';
import PaymentSuccessModal from './PaymentSuccessModal';
import { Mail, CreditCard, Zap, Shield, ArrowRight, Loader2 } from 'lucide-react';

export default function PurchaseForm() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPayment, setShowPayment] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [orderId, setOrderId] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [augmentEmail, setAugmentEmail] = useState('');
  const [error, setError] = useState('');
  const [price, setPrice] = useState(99);
  const [priceDisplay, setPriceDisplay] = useState('¥99');

  useEffect(() => {
    // 获取价格配置
    const fetchPrice = async () => {
      try {
        const response = await fetch('/api/config/price');
        const data = await response.json();
        if (data.success) {
          setPrice(data.data.price);
          setPriceDisplay(data.data.priceDisplay);
        }
      } catch (error) {
        console.error('获取价格配置失败:', error);
      }
    };

    fetchPrice();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // 验证邮箱
    if (!email.trim()) {
      setError('请输入邮箱地址');
      return;
    }

    if (!isValidEmail(email)) {
      setError('请输入有效的邮箱地址');
      return;
    }

    setIsLoading(true);

    try {
      // 创建订单
      const response = await fetch('/api/orders/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userEmail: email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '创建订单失败');
      }

      // 显示支付模态框
      setOrderId(data.data.orderId);
      setQrCodeUrl(data.data.qrCodeUrl);
      setShowPayment(true);
    } catch (error) {
      console.error('创建订单失败:', error);
      setError(error instanceof Error ? error.message : '创建订单失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentSuccess = (receivedAugmentEmail: string) => {
    setShowPayment(false);
    setAugmentEmail(receivedAugmentEmail);
    setShowSuccess(true);
  };

  const handlePaymentClose = () => {
    setShowPayment(false);
    setOrderId('');
    setQrCodeUrl('');
  };

  const handleSuccessClose = () => {
    setShowSuccess(false);
    setAugmentEmail('');
    setEmail('');
    setOrderId('');
    setQrCodeUrl('');
  };

  return (
    <>
      <div className="space-y-8">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 邮箱输入区域 */}
          <div className="space-y-3">
            <label htmlFor="email" className="flex items-center space-x-2 text-sm font-semibold text-white">
              <Mail className="w-4 h-4" />
              <span>接收邮箱</span>
            </label>
            <div className="relative">
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="请输入您的邮箱地址"
                className="w-full px-4 py-4 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 placeholder-white/50 text-white font-medium shadow-lg"
                disabled={isLoading}
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-4">
                <Mail className="w-5 h-5 text-white/40" />
              </div>
            </div>
            <p className="text-sm text-white/70 flex items-center space-x-2">
              <Zap className="w-4 h-4" />
              <span>账号验证码会发送至此邮箱</span>
            </p>
          </div>

          {/* 价格显示 */}
          <div className="bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-md rounded-2xl p-6 border border-white/20 shadow-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-600 rounded-xl flex items-center justify-center">
                  <CreditCard className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="text-white/70 text-sm">支付金额</p>
                  <p className="text-white font-semibold">Augment 账号</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold bg-gradient-to-r from-emerald-400 to-green-400 bg-clip-text text-transparent">
                  {priceDisplay}
                </p>
                <p className="text-white/60 text-sm">一次性付费</p>
              </div>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="bg-gradient-to-r from-red-500/20 to-pink-500/20 backdrop-blur-md border border-red-400/30 rounded-2xl p-4 animate-in slide-in-from-top duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-sm font-bold">!</span>
                </div>
                <span className="text-red-100 font-medium">{error}</span>
              </div>
            </div>
          )}

          {/* 服务特色 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white/5 backdrop-blur-md rounded-xl p-4 border border-white/10">
              <div className="flex items-center space-x-3">
                <Shield className="w-5 h-5 text-emerald-400" />
                <span className="text-white/90 text-sm font-medium">安全保障</span>
              </div>
            </div>
            <div className="bg-white/5 backdrop-blur-md rounded-xl p-4 border border-white/10">
              <div className="flex items-center space-x-3">
                <Zap className="w-5 h-5 text-blue-400" />
                <span className="text-white/90 text-sm font-medium">即时交付</span>
              </div>
            </div>
          </div>

          {/* 购买按钮 */}
          <button
            type="submit"
            disabled={isLoading}
            className="group w-full bg-gradient-to-r from-emerald-500 via-blue-500 to-purple-600 hover:from-emerald-600 hover:via-blue-600 hover:to-purple-700 disabled:from-gray-500 disabled:to-gray-600 text-white font-bold py-5 px-8 rounded-2xl transition-all duration-300 flex items-center justify-center shadow-2xl hover:shadow-3xl transform hover:scale-105 disabled:transform-none disabled:hover:scale-100 relative overflow-hidden"
          >
            {/* 按钮背景动画 */}
            <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

            <div className="relative flex items-center space-x-3">
              {isLoading ? (
                <>
                  <Loader2 className="w-6 h-6 animate-spin" />
                  <span className="text-lg">创建订单中...</span>
                </>
              ) : (
                <>
                  <CreditCard className="w-6 h-6" />
                  <span className="text-lg">立即购买</span>
                  <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
                </>
              )}
            </div>
          </button>
        </form>

        {/* 底部提示 */}
        <div className="text-center">
          <p className="text-sm text-white/60 leading-relaxed">
            支付成功后，账号信息将立即发送至您的邮箱
          </p>
        </div>
      </div>

      {/* 支付模态框 */}
      {showPayment && (
        <PaymentModal
          orderId={orderId}
          qrCodeUrl={qrCodeUrl}
          amount={price}
          onSuccess={handlePaymentSuccess}
          onClose={handlePaymentClose}
        />
      )}

      {/* 支付成功模态框 */}
      {showSuccess && (
        <PaymentSuccessModal
          augmentEmail={augmentEmail}
          userEmail={email}
          onClose={handleSuccessClose}
        />
      )}
    </>
  );
}
