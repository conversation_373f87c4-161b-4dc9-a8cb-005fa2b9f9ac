'use client';

import { useState, useEffect } from 'react';
import QRCode from 'qrcode';
import { X, Clock, Smartphone, CheckCircle2, AlertCircle } from 'lucide-react';

interface PaymentModalProps {
  orderId: string;
  qrCodeUrl: string;
  amount: number;
  onSuccess: (augmentEmail: string) => void;
  onClose: () => void;
}

export default function PaymentModal({
  orderId,
  qrCodeUrl,
  amount,
  onSuccess,
  onClose
}: PaymentModalProps) {
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState('');
  const [isPolling, setIsPolling] = useState(true);
  const [timeLeft, setTimeLeft] = useState(15 * 60); // 15分钟倒计时

  // 生成二维码
  useEffect(() => {
    const generateQRCode = async () => {
      try {
        const dataUrl = await QRCode.toDataURL(qrCodeUrl, {
          width: 256,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });
        setQrCodeDataUrl(dataUrl);
      } catch (error) {
        console.error('生成二维码失败:', error);
      }
    };

    if (qrCodeUrl) {
      generateQRCode();
    }
  }, [qrCodeUrl]);

  // 轮询支付状态
  useEffect(() => {
    if (!isPolling) return;

    const pollPaymentStatus = async () => {
      try {
        const response = await fetch(`/api/orders/status?orderId=${orderId}`);
        const data = await response.json();

        if (data.success && data.data.status === 'COMPLETED' && data.data.augmentEmail) {
          setIsPolling(false);
          onSuccess(data.data.augmentEmail);
        }
      } catch (error) {
        console.error('查询支付状态失败:', error);
      }
    };

    const interval = setInterval(pollPaymentStatus, 3000); // 每3秒查询一次

    return () => clearInterval(interval);
  }, [orderId, isPolling, onSuccess]);

  // 倒计时
  useEffect(() => {
    if (timeLeft <= 0) {
      setIsPolling(false);
      return;
    }

    const timer = setTimeout(() => {
      setTimeLeft(timeLeft - 1);
    }, 1000);

    return () => clearTimeout(timer);
  }, [timeLeft]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleClose = () => {
    setIsPolling(false);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-in fade-in duration-300">
      <div className="bg-white/95 backdrop-blur-xl rounded-3xl max-w-lg w-full shadow-2xl border border-white/20 relative overflow-hidden animate-in zoom-in-95 duration-300">
        {/* 装饰性背景 */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-emerald-500/20 to-cyan-500/20 rounded-full blur-2xl"></div>

        <div className="relative z-10 p-8">
          {/* 关闭按钮 */}
          <button
            onClick={handleClose}
            className="absolute top-6 right-6 w-10 h-10 bg-white/80 backdrop-blur-md rounded-full flex items-center justify-center text-gray-600 hover:text-gray-800 hover:bg-white/90 transition-all duration-200 shadow-lg"
          >
            <X className="w-5 h-5" />
          </button>

          {/* 标题区域 */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
              <Smartphone className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">微信支付</h3>
            <p className="text-gray-600 leading-relaxed">使用微信扫描二维码完成支付</p>
          </div>

          {/* 订单信息 */}
          <div className="bg-gradient-to-r from-blue-50/80 to-purple-50/80 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/50">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600 font-medium">商品</span>
                <span className="font-semibold text-gray-900">Augment 账号</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600 font-medium">订单号</span>
                <span className="font-mono text-sm text-gray-700 bg-white/60 px-3 py-1 rounded-lg">{orderId.slice(-8)}</span>
              </div>
              <div className="flex justify-between items-center pt-2 border-t border-white/50">
                <span className="text-gray-600 font-medium">支付金额</span>
                <span className="font-bold text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">¥{amount}</span>
              </div>
            </div>
          </div>

          {/* 二维码区域 */}
          <div className="text-center mb-8">
            {qrCodeDataUrl ? (
              <div className="inline-block p-6 bg-white/90 backdrop-blur-md rounded-3xl shadow-2xl border border-white/50 hover:shadow-3xl transition-all duration-300">
                <img
                  src={qrCodeDataUrl}
                  alt="支付二维码"
                  className="w-56 h-56 mx-auto rounded-2xl"
                />
                <div className="mt-4 flex items-center justify-center space-x-2 text-green-600">
                  <CheckCircle2 className="w-5 h-5" />
                  <span className="font-medium">二维码已生成</span>
                </div>
              </div>
            ) : (
              <div className="w-56 h-56 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center shadow-xl">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-600 border-t-transparent mx-auto mb-4"></div>
                  <p className="text-gray-600 font-medium">生成二维码中...</p>
                </div>
              </div>
            )}
          </div>

          {/* 倒计时和状态 */}
          <div className="text-center mb-6">
            {timeLeft > 0 ? (
              <div className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-2xl p-4 border border-orange-200/50">
                <div className="flex items-center justify-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-full flex items-center justify-center">
                    <Clock className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-left">
                    <p className="text-sm text-orange-600 font-medium">支付剩余时间</p>
                    <p className="text-xl font-bold text-orange-700">{formatTime(timeLeft)}</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-gradient-to-r from-red-50 to-pink-50 rounded-2xl p-4 border border-red-200/50">
                <div className="flex items-center justify-center space-x-3">
                  <AlertCircle className="w-8 h-8 text-red-500" />
                  <div className="text-left">
                    <p className="text-red-600 font-semibold">支付已超时</p>
                    <p className="text-sm text-red-500">请重新创建订单</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 支付状态 */}
          {isPolling && timeLeft > 0 && (
            <div className="text-center mb-6">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-4 border border-blue-200/50">
                <div className="flex items-center justify-center space-x-3">
                  <div className="relative">
                    <div className="animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent"></div>
                    <div className="absolute inset-0 rounded-full bg-blue-100/50"></div>
                  </div>
                  <div className="text-left">
                    <p className="text-blue-700 font-semibold">等待支付中</p>
                    <p className="text-sm text-blue-600">请使用微信扫码支付</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 支付说明 */}
          <div className="text-center">
            <div className="bg-gradient-to-r from-gray-50 to-blue-50/50 rounded-2xl p-4 border border-gray-200/50">
              <p className="text-sm text-gray-600 leading-relaxed">
                <span className="font-medium text-gray-700">💡 温馨提示：</span><br />
                支付成功后，Augment 账号信息将立即发送至您的邮箱
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
