{"name": "aug-pay", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@larksuiteoapi/node-sdk": "^1.54.0", "@prisma/client": "^6.13.0", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.53.0", "@types/qrcode": "^1.5.5", "@types/uuid": "^10.0.0", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "lucide-react": "^0.534.0", "next": "15.4.5", "prisma": "^6.13.0", "qrcode": "^1.5.4", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}