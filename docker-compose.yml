version: '3.8'

services:
  aug-pay:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      # 挂载环境变量文件
      - ./.env.local:/app/.env.local:ro
      # 挂载证书文件目录
      - ./certs:/app/certs:ro
      # 挂载数据库文件（持久化）
      - ./data:/app/data
    environment:
      # 覆盖数据库路径到挂载的数据目录
      - DATABASE_URL=file:./data/dev.db
      # 设置 Node.js 环境
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
