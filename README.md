# Augment 账号自动售卖系统

一个基于 Next.js 的自动售卖 Augment 账号的网站，支持微信支付 Native 支付。

## 功能特性

- 🚀 **即时交付** - 支付成功后立即获得 Augment 账号
- 💳 **微信支付** - 支持微信 Native 扫码支付
- 🔒 **安全可靠** - 完整的支付验证和订单管理
- 📱 **响应式设计** - 支持桌面和移动设备
- 🎯 **简洁流程** - 填写邮箱 → 扫码支付 → 获得账号

## 技术栈

- **前端**: Next.js 14, React, TypeScript, Tailwind CSS
- **后端**: Next.js API Routes, Prisma ORM
- **数据库**: PostgreSQL (推荐 Supabase)
- **支付**: 微信支付 Native API
- **部署**: Vercel (推荐)

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API 路由
│   │   ├── orders/        # 订单相关 API
│   │   └── payment/       # 支付相关 API
│   ├── test/              # 测试页面
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页
├── components/            # React 组件
│   ├── PurchaseForm.tsx   # 购买表单
│   └── PaymentModal.tsx   # 支付模态框
├── lib/                   # 工具库
│   ├── database.ts        # 数据库操作
│   ├── wechat-pay.ts      # 微信支付集成
│   ├── supabase.ts        # Supabase 配置
│   └── utils.ts           # 工具函数
└── types/                 # TypeScript 类型定义
    └── index.ts
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 环境配置

配置 `.env.local` 文件中的环境变量：

```env
# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/augpay"

# 微信支付配置
WECHAT_APP_ID="your-wechat-app-id"
WECHAT_MCH_ID="your-wechat-mch-id"
WECHAT_API_KEY="your-wechat-api-key"

# 应用配置
NEXT_PUBLIC_APP_URL="http://localhost:3000"
AUGMENT_ACCOUNT_PRICE="99"
```

### 3. 数据库设置

```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma db push
```

### 4. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 5. 测试功能

访问 [http://localhost:3000/test](http://localhost:3000/test) 进行功能测试。

## 微信支付配置

1. 申请微信支付商户号
2. 获取 AppID、商户号、API密钥
3. 配置支付回调URL: `https://your-domain.com/api/payment/notify`

## 部署

推荐使用 Vercel 部署：

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量
4. 部署

## 后续开发

- [ ] Augment 账号池管理系统
- [ ] 邮件通知功能
- [ ] 订单管理后台
- [ ] 支付统计和报表

## 许可证

MIT License
