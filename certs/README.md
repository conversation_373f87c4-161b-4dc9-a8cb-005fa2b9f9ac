# 微信支付 API v3 证书配置说明

## 📋 需要的证书文件

为了使用微信支付 API v3，您需要从微信商户平台下载以下证书文件：

### 1. 商户私钥文件
- **文件名**: `apiclient_key.pem`
- **说明**: 商户私钥，用于生成请求签名
- **获取方式**: 从微信商户平台下载

### 2. 商户证书文件
- **文件名**: `apiclient_cert.pem`
- **说明**: 商户证书，包含商户证书序列号
- **获取方式**: 从微信商户平台下载

## 🔧 如何获取证书文件

### 步骤 1：登录微信商户平台
访问 [微信商户平台](https://pay.weixin.qq.com) 并登录您的商户账号。

### 步骤 2：下载 API 证书
1. 进入 **账户中心** → **账户设置** → **API安全**
2. 点击 **下载证书**
3. 下载证书压缩包

### 步骤 3：解压并放置证书文件
1. 解压下载的证书压缩包
2. 将 `apiclient_key.pem` 和 `apiclient_cert.pem` 文件复制到此目录
3. 确保文件权限安全（建议设置为 600）

## 🔑 获取商户证书序列号

### 方法 1：使用 OpenSSL 命令
```bash
openssl x509 -in apiclient_cert.pem -noout -serial
```

### 方法 2：在线工具
访问微信支付官方提供的证书工具页面获取序列号。

## ⚙️ 配置环境变量

获取证书文件和序列号后，请在 `.env.local` 文件中配置：

```env
# 微信支付 API v3 配置
WECHAT_APP_ID="您的应用ID"
WECHAT_MCH_ID="您的商户号"
WECHAT_API_V3_KEY="您的API v3密钥"

# 证书文件路径
WECHAT_CERT_PATH="./certs/apiclient_cert.pem"
WECHAT_KEY_PATH="./certs/apiclient_key.pem"
WECHAT_CERT_SERIAL_NUMBER="您的商户证书序列号"
```

## 🔒 安全注意事项

1. **不要将证书文件提交到版本控制系统**
2. **设置适当的文件权限**：
   ```bash
   chmod 600 apiclient_key.pem
   chmod 600 apiclient_cert.pem
   ```
3. **定期更新证书**（证书有效期通常为1年）
4. **妥善保管证书文件**，避免泄露

## 📁 目录结构

```
certs/
├── README.md              # 本说明文件
├── apiclient_key.pem      # 商户私钥文件（需要下载）
└── apiclient_cert.pem     # 商户证书文件（需要下载）
```

## ❓ 常见问题

### Q: 证书文件在哪里下载？
A: 登录微信商户平台 → 账户中心 → 账户设置 → API安全 → 下载证书

### Q: 如何获取 API v3 密钥？
A: 在微信商户平台的 API安全 页面中设置 API v3 密钥

### Q: 证书序列号在哪里查看？
A: 使用 OpenSSL 命令或微信支付官方工具获取

### Q: 证书文件权限应该如何设置？
A: 建议设置为 600，确保只有文件所有者可以读写

## 🚀 完成配置后

配置完成后，重启应用程序，微信支付 API v3 功能即可正常使用。
