// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 订单表
model Order {
  id          String      @id @default(cuid())
  userEmail   String      @map("user_email")
  amount      Int         // 金额（分）
  status      OrderStatus @default(PENDING)
  augmentEmail String?    @map("augment_email") // 分配的 Augment 账号邮箱
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")
  
  // 关联支付记录
  payments    Payment[]
  
  @@map("orders")
}

// 支付表
model Payment {
  id            String        @id @default(cuid())
  orderId       String        @map("order_id")
  amount        Int           // 金额（分）
  status        PaymentStatus @default(PENDING)
  wechatOrderId String?       @map("wechat_order_id") // 微信订单号
  qrCodeUrl     String?       @map("qr_code_url")     // 支付二维码URL
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @updatedAt @map("updated_at")
  
  // 关联订单
  order         Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  @@map("payments")
}

// Augment 账号池表（后续开发）
model AugmentAccount {
  id        String              @id @default(cuid())
  email     String              @unique
  password  String
  status    AugmentAccountStatus @default(AVAILABLE)
  assignedAt DateTime?          @map("assigned_at")
  createdAt DateTime            @default(now()) @map("created_at")
  updatedAt DateTime            @updatedAt @map("updated_at")
  
  @@map("augment_accounts")
}

// 订单状态枚举
enum OrderStatus {
  PENDING    // 待支付
  PAID       // 已支付
  COMPLETED  // 已完成（已分配账号）
  FAILED     // 失败
  CANCELLED  // 已取消
}

// 支付状态枚举
enum PaymentStatus {
  PENDING   // 待支付
  SUCCESS   // 支付成功
  FAILED    // 支付失败
  REFUNDED  // 已退款
}

// Augment 账号状态枚举
enum AugmentAccountStatus {
  AVAILABLE // 可用
  ASSIGNED  // 已分配
  DISABLED  // 已禁用
}
