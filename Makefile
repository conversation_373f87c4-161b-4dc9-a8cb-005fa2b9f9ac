# Docker 操作 Makefile

.PHONY: help build up down logs restart clean

# 默认目标
help:
	@echo "可用命令："
	@echo "  build    - 构建 Docker 镜像"
	@echo "  up       - 启动容器"
	@echo "  down     - 停止容器"
	@echo "  logs     - 查看日志"
	@echo "  restart  - 重启容器"
	@echo "  clean    - 清理容器和镜像"
	@echo "  shell    - 进入容器 shell"

# 构建镜像
build:
	docker-compose build

# 启动容器
up:
	docker-compose up -d

# 停止容器
down:
	docker-compose down

# 查看日志
logs:
	docker-compose logs -f

# 重启容器
restart:
	docker-compose restart

# 清理容器和镜像
clean:
	docker-compose down --rmi all --volumes --remove-orphans

# 进入容器 shell
shell:
	docker-compose exec aug-pay sh

# 一键部署（构建并启动）
deploy: build up

# 查看容器状态
status:
	docker-compose ps
