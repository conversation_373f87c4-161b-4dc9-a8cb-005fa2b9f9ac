# Docker 部署指南

本项目支持使用 Docker 进行容器化部署。

## 前置要求

- Docker
- Docker Compose（可选，推荐）

## 快速开始

### 方法一：使用 Docker Compose（推荐）

1. 确保项目根目录下有 `.env.local` 文件，包含所有必要的环境变量
2. 确保 `certs` 目录下有微信支付证书文件
3. 运行以下命令：

```bash
# 构建并启动容器
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止容器
docker-compose down
```

### 方法二：使用 Docker 命令

1. 构建镜像：

```bash
docker build -t aug-pay .
```

2. 创建数据目录：

```bash
mkdir -p ./data
```

3. 运行容器：

```bash
docker run -d \
  --name aug-pay \
  -p 3000:3000 \
  -v $(pwd)/.env.local:/app/.env.local:ro \
  -v $(pwd)/certs:/app/certs:ro \
  -v $(pwd)/data:/app/data \
  -e DATABASE_URL=file:./data/dev.db \
  aug-pay
```

## 目录挂载说明

- `.env.local`: 环境变量文件（只读挂载）
- `certs/`: 微信支付证书目录（只读挂载）
- `data/`: 数据库文件目录（读写挂载，用于持久化数据）

## 环境变量

容器会自动读取挂载的 `.env.local` 文件。主要环境变量包括：

- `DATABASE_URL`: 数据库连接字符串（容器中会自动设置为 `file:./data/dev.db`）
- `WECHAT_*`: 微信支付相关配置
- `FEISHU_*`: 飞书 API 相关配置
- `SUPABASE_*`: Supabase 相关配置

## 数据持久化

- 数据库文件存储在 `./data/dev.db`
- 首次启动时会自动初始化数据库
- 数据通过 Docker 卷挂载实现持久化

## 健康检查

容器包含健康检查功能，会定期检查应用状态：

```bash
# 查看容器健康状态
docker ps

# 手动健康检查
curl http://localhost:3000/api/health
```

## 日志查看

```bash
# 使用 Docker Compose
docker-compose logs -f

# 使用 Docker 命令
docker logs -f aug-pay
```

## 故障排除

### 1. 权限问题

如果遇到文件权限问题，确保挂载的目录有正确的权限：

```bash
chmod -R 755 ./data
chmod -R 644 ./certs
chmod 644 .env.local
```

### 2. 数据库初始化失败

如果数据库初始化失败，可以删除数据文件重新初始化：

```bash
rm -f ./data/dev.db
docker-compose restart
```

### 3. 端口冲突

如果 3000 端口被占用，可以修改 `docker-compose.yml` 中的端口映射：

```yaml
ports:
  - "8080:3000"  # 将本地 8080 端口映射到容器 3000 端口
```

## 生产环境部署

在生产环境中，建议：

1. 使用环境变量而不是 `.env.local` 文件
2. 使用外部数据库（如 PostgreSQL）而不是 SQLite
3. 配置反向代理（如 Nginx）
4. 启用 HTTPS
5. 配置日志收集和监控

## 更新应用

```bash
# 停止容器
docker-compose down

# 重新构建镜像
docker-compose build

# 启动新容器
docker-compose up -d
```
