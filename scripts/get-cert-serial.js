#!/usr/bin/env node

/**
 * 微信支付证书序列号提取工具
 * 用于从商户证书文件中提取序列号
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

function getCertificateSerial(certPath) {
  try {
    // 检查证书文件是否存在
    if (!fs.existsSync(certPath)) {
      console.error(`❌ 证书文件不存在: ${certPath}`);
      console.log('\n📋 请确保已将 apiclient_cert.pem 文件放置在 certs/ 目录中');
      console.log('📖 详细说明请查看: certs/README.md');
      return null;
    }

    // 读取证书文件
    const certContent = fs.readFileSync(certPath, 'utf8');
    
    // 创建 X509 证书对象
    const cert = new crypto.X509Certificate(certContent);
    
    // 获取序列号（十六进制格式）
    const serialNumber = cert.serialNumber;
    
    console.log('🎉 成功提取证书序列号！');
    console.log('📄 证书文件:', certPath);
    console.log('🔢 证书序列号:', serialNumber);
    console.log('\n📝 请将此序列号配置到 .env.local 文件中：');
    console.log(`WECHAT_CERT_SERIAL_NUMBER="${serialNumber}"`);
    
    return serialNumber;
    
  } catch (error) {
    console.error('❌ 提取证书序列号失败:', error.message);
    
    if (error.message.includes('Invalid certificate')) {
      console.log('\n💡 可能的解决方案：');
      console.log('1. 确保证书文件格式正确（PEM 格式）');
      console.log('2. 确保证书文件完整且未损坏');
      console.log('3. 重新从微信商户平台下载证书文件');
    }
    
    return null;
  }
}

function main() {
  console.log('🔧 微信支付证书序列号提取工具');
  console.log('=====================================\n');
  
  // 默认证书路径
  const defaultCertPath = path.join(__dirname, '../certs/apiclient_cert.pem');
  
  // 检查命令行参数
  const certPath = process.argv[2] || defaultCertPath;
  
  console.log('🔍 正在检查证书文件...');
  
  const serialNumber = getCertificateSerial(certPath);
  
  if (serialNumber) {
    console.log('\n✅ 证书序列号提取完成！');
    console.log('\n🚀 下一步：');
    console.log('1. 将序列号配置到 .env.local 文件');
    console.log('2. 确保 API v3 密钥已正确配置');
    console.log('3. 重启应用程序测试微信支付功能');
  } else {
    console.log('\n❌ 证书序列号提取失败');
    console.log('\n📖 请查看 certs/README.md 获取详细配置说明');
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { getCertificateSerial };
